{"version": 3, "sources": ["../../vue3-seamless-scroll/dist/vue3-seamless-scroll.es.js"], "sourcesContent": ["import { defineComponent, ref, computed, watch, nextTick, onBeforeMount, onMounted, createVNode, Fragment, getCurrentInstance } from 'vue';\n\n/* eslint-disable no-undefined,no-param-reassign,no-shadow */\n\n/**\n * Throttle execution of a function. Especially useful for rate limiting\n * execution of handlers on events like resize and scroll.\n *\n * @param  {number}    delay -          A zero-or-greater delay in milliseconds. For event callbacks, values around 100 or 250 (or even higher) are most useful.\n * @param  {boolean}   [noTrailing] -   Optional, defaults to false. If noTrailing is true, callback will only execute every `delay` milliseconds while the\n *                                    throttled-function is being called. If noTrailing is false or unspecified, callback will be executed one final time\n *                                    after the last throttled-function call. (After the throttled-function has not been called for `delay` milliseconds,\n *                                    the internal counter is reset).\n * @param  {Function}  callback -       A function to be executed after delay milliseconds. The `this` context and all arguments are passed through, as-is,\n *                                    to `callback` when the throttled-function is executed.\n * @param  {boolean}   [debounceMode] - If `debounceMode` is true (at begin), schedule `clear` to execute after `delay` ms. If `debounceMode` is false (at end),\n *                                    schedule `callback` to execute after `delay` ms.\n *\n * @returns {Function}  A new, throttled, function.\n */\nfunction throttle (delay, noTrailing, callback, debounceMode) {\n  /*\n   * After wrapper has stopped being called, this timeout ensures that\n   * `callback` is executed at the proper times in `throttle` and `end`\n   * debounce modes.\n   */\n  var timeoutID;\n  var cancelled = false; // Keep track of the last time `callback` was executed.\n\n  var lastExec = 0; // Function to clear existing timeout\n\n  function clearExistingTimeout() {\n    if (timeoutID) {\n      clearTimeout(timeoutID);\n    }\n  } // Function to cancel next exec\n\n\n  function cancel() {\n    clearExistingTimeout();\n    cancelled = true;\n  } // `noTrailing` defaults to falsy.\n\n\n  if (typeof noTrailing !== 'boolean') {\n    debounceMode = callback;\n    callback = noTrailing;\n    noTrailing = undefined;\n  }\n  /*\n   * The `wrapper` function encapsulates all of the throttling / debouncing\n   * functionality and when executed will limit the rate at which `callback`\n   * is executed.\n   */\n\n\n  function wrapper() {\n    for (var _len = arguments.length, arguments_ = new Array(_len), _key = 0; _key < _len; _key++) {\n      arguments_[_key] = arguments[_key];\n    }\n\n    var self = this;\n    var elapsed = Date.now() - lastExec;\n\n    if (cancelled) {\n      return;\n    } // Execute `callback` and update the `lastExec` timestamp.\n\n\n    function exec() {\n      lastExec = Date.now();\n      callback.apply(self, arguments_);\n    }\n    /*\n     * If `debounceMode` is true (at begin) this is used to clear the flag\n     * to allow future `callback` executions.\n     */\n\n\n    function clear() {\n      timeoutID = undefined;\n    }\n\n    if (debounceMode && !timeoutID) {\n      /*\n       * Since `wrapper` is being called for the first time and\n       * `debounceMode` is true (at begin), execute `callback`.\n       */\n      exec();\n    }\n\n    clearExistingTimeout();\n\n    if (debounceMode === undefined && elapsed > delay) {\n      /*\n       * In throttle mode, if `delay` time has been exceeded, execute\n       * `callback`.\n       */\n      exec();\n    } else if (noTrailing !== true) {\n      /*\n       * In trailing throttle mode, since `delay` time has not been\n       * exceeded, schedule `callback` to execute `delay` ms after most\n       * recent execution.\n       *\n       * If `debounceMode` is true (at begin), schedule `clear` to execute\n       * after `delay` ms.\n       *\n       * If `debounceMode` is false (at end), schedule `callback` to\n       * execute after `delay` ms.\n       */\n      timeoutID = setTimeout(debounceMode ? clear : exec, debounceMode === undefined ? delay - elapsed : delay);\n    }\n  }\n\n  wrapper.cancel = cancel; // Return the wrapper function.\n\n  return wrapper;\n}\n\nfunction useExpose(apis) {\n  const instance = getCurrentInstance();\n\n  if (instance) {\n    Object.assign(instance.proxy, apis);\n  }\n}\n\nconst Props = {\n  // 是否开启自动滚动\n  modelValue: {\n    type: Boolean,\n    default: true\n  },\n  // 原始数据列表\n  list: {\n    type: Array,\n    required: true,\n    default: []\n  },\n  // 步进速度，step 需是单步大小的约数\n  step: {\n    type: Number,\n    default: 1\n  },\n  // 开启滚动的数据量\n  limitScrollNum: {\n    type: Number,\n    default: 3\n  },\n  // 是否开启鼠标悬停\n  hover: {\n    type: Boolean,\n    default: false\n  },\n  // 控制滚动方向\n  direction: {\n    type: String,\n    default: \"up\"\n  },\n  // 单步运动停止的高度\n  singleHeight: {\n    type: Number,\n    default: 0\n  },\n  // 单步运动停止的宽度\n  singleWidth: {\n    type: Number,\n    default: 0\n  },\n  // 单步停止等待时间 (默认值 1000ms)\n  singleWaitTime: {\n    type: Number,\n    default: 1000\n  },\n  // 是否开启 rem 度量\n  isRemUnit: {\n    type: Boolean,\n    default: false\n  },\n  // 开启数据更新监听\n  isWatch: {\n    type: Boolean,\n    default: true\n  },\n  // 动画时间\n  delay: {\n    type: Number,\n    default: 0\n  },\n  // 动画方式\n  ease: {\n    type: [String, Object],\n    default: \"ease-in\"\n  },\n  // 动画循环次数，-1 表示一直动画\n  count: {\n    type: Number,\n    default: -1\n  },\n  // 拷贝几份滚动列表\n  copyNum: {\n    type: Number,\n    default: 1\n  },\n  // 开启鼠标悬停时支持滚轮滚动\n  wheel: {\n    type: Boolean,\n    default: false\n  },\n  // 启用单行滚动\n  singleLine: {\n    type: Boolean,\n    default: false\n  }\n};\n\nglobalThis.window.cancelAnimationFrame = function () {\n  return globalThis.window.cancelAnimationFrame || // @ts-ignore\n  globalThis.window.webkitCancelAnimationFrame || // @ts-ignore\n  globalThis.window.mozCancelAnimationFrame || // @ts-ignore\n  globalThis.window.oCancelAnimationFrame || // @ts-ignore\n  globalThis.window.msCancelAnimationFrame || function (id) {\n    return globalThis.window.clearTimeout(id);\n  };\n}();\n\nglobalThis.window.requestAnimationFrame = function () {\n  return globalThis.window.requestAnimationFrame || // @ts-ignore\n  globalThis.window.webkitRequestAnimationFrame || // @ts-ignore\n  globalThis.window.mozRequestAnimationFrame || // @ts-ignore\n  globalThis.window.oRequestAnimationFrame || // @ts-ignore\n  globalThis.window.msRequestAnimationFrame || function (callback) {\n    return globalThis.window.setTimeout(callback, 1000 / 60);\n  };\n}();\n\nfunction dataWarm(list) {\n  if (list && typeof list !== \"boolean\" && list.length > 100) {\n    console.warn(`数据达到了${list.length}条有点多哦~,可能会造成部分老旧浏览器卡顿。`);\n  }\n}\n\nconst Vue3SeamlessScroll = defineComponent({\n  name: \"vue3-seamless-scroll\",\n  inheritAttrs: false,\n  props: Props,\n  emits: [\"stop\", \"count\", \"move\"],\n\n  setup(_props, {\n    slots,\n    emit,\n    attrs\n  }) {\n    const props = _props;\n    const scrollRef = ref(null);\n    const slotListRef = ref(null);\n    const realBoxRef = ref(null);\n    const reqFrame = ref(null);\n    const singleWaitTimeout = ref(null);\n    const realBoxWidth = ref(0);\n    const realBoxHeight = ref(0);\n    const xPos = ref(0);\n    const yPos = ref(0);\n    const isHover = ref(false);\n\n    const _count = ref(0);\n\n    const isScroll = computed(() => props.list ? props.list.length >= props.limitScrollNum : false);\n    const realBoxStyle = computed(() => {\n      return {\n        width: realBoxWidth.value ? `${realBoxWidth.value}px` : \"auto\",\n        transform: `translate(${xPos.value}px,${yPos.value}px)`,\n        // @ts-ignore\n        transition: `all ${typeof props.ease === \"string\" ? props.ease : \"cubic-bezier(\" + props.ease.x1 + \",\" + props.ease.y1 + \",\" + props.ease.x2 + \",\" + props.ease.y2 + \")\"} ${props.delay}ms`,\n        overflow: \"hidden\",\n        display: props.singleLine ? \"flex\" : \"block\"\n      };\n    });\n    const isHorizontal = computed(() => props.direction == \"left\" || props.direction == \"right\");\n    const floatStyle = computed(() => {\n      return isHorizontal.value ? {\n        float: \"left\",\n        overflow: \"hidden\",\n        display: props.singleLine ? \"flex\" : \"block\",\n        flexShrink: props.singleLine ? 0 : 1\n      } : {\n        overflow: \"hidden\"\n      };\n    });\n    const baseFontSize = computed(() => {\n      return props.isRemUnit ? parseInt(globalThis.window.getComputedStyle(globalThis.document.documentElement, null).fontSize) : 1;\n    });\n    const realSingleStopWidth = computed(() => props.singleWidth * baseFontSize.value);\n    const realSingleStopHeight = computed(() => props.singleHeight * baseFontSize.value);\n    const step = computed(() => {\n      let singleStep;\n      let _step = props.step;\n\n      if (isHorizontal.value) {\n        singleStep = realSingleStopWidth.value;\n      } else {\n        singleStep = realSingleStopHeight.value;\n      }\n\n      if (singleStep > 0 && singleStep % _step > 0) {\n        console.error(\"如果设置了单步滚动，step 需是单步大小的约数，否则无法保证单步滚动结束的位置是否准确。~~~~~\");\n      }\n\n      return _step;\n    });\n\n    const cancle = () => {\n      cancelAnimationFrame(reqFrame.value);\n      reqFrame.value = null;\n    };\n\n    const animation = (_direction, _step, isWheel) => {\n      reqFrame.value = requestAnimationFrame(function () {\n        const h = realBoxHeight.value / 2;\n        const w = realBoxWidth.value / 2;\n\n        if (_direction === \"up\") {\n          if (Math.abs(yPos.value) >= h) {\n            yPos.value = 0;\n            _count.value += 1;\n            emit(\"count\", _count.value);\n          }\n\n          yPos.value -= _step;\n        } else if (_direction === \"down\") {\n          if (yPos.value >= 0) {\n            yPos.value = h * -1;\n            _count.value += 1;\n            emit(\"count\", _count.value);\n          }\n\n          yPos.value += _step;\n        } else if (_direction === \"left\") {\n          if (Math.abs(xPos.value) >= w) {\n            xPos.value = 0;\n            _count.value += 1;\n            emit(\"count\", _count.value);\n          }\n\n          xPos.value -= _step;\n        } else if (_direction === \"right\") {\n          if (xPos.value >= 0) {\n            xPos.value = w * -1;\n            _count.value += 1;\n            emit(\"count\", _count.value);\n          }\n\n          xPos.value += _step;\n        }\n\n        if (isWheel) {\n          return;\n        }\n\n        let {\n          singleWaitTime\n        } = props;\n\n        if (singleWaitTimeout.value) {\n          clearTimeout(singleWaitTimeout.value);\n        }\n\n        if (!!realSingleStopHeight.value) {\n          if (Math.abs(yPos.value) % realSingleStopHeight.value < _step) {\n            singleWaitTimeout.value = setTimeout(() => {\n              move();\n            }, singleWaitTime);\n          } else {\n            move();\n          }\n        } else if (!!realSingleStopWidth.value) {\n          if (Math.abs(xPos.value) % realSingleStopWidth.value < _step) {\n            singleWaitTimeout.value = setTimeout(() => {\n              move();\n            }, singleWaitTime);\n          } else {\n            move();\n          }\n        } else {\n          move();\n        }\n      });\n    };\n\n    const move = () => {\n      cancle();\n\n      if (isHover.value || !isScroll.value || _count.value === props.count) {\n        emit(\"stop\", _count.value);\n        _count.value = 0;\n        return;\n      }\n\n      animation(props.direction, step.value, false);\n    };\n\n    const initMove = () => {\n      dataWarm(props.list);\n\n      if (isHorizontal.value) {\n        let slotListWidth = slotListRef.value.offsetWidth;\n        slotListWidth = slotListWidth * 2 + 1;\n        realBoxWidth.value = slotListWidth;\n      }\n\n      if (isScroll.value) {\n        realBoxHeight.value = realBoxRef.value.offsetHeight;\n\n        if (props.modelValue) {\n          move();\n        }\n      } else {\n        cancle();\n        yPos.value = xPos.value = 0;\n      }\n    };\n\n    const startMove = () => {\n      isHover.value = false;\n      move();\n    };\n\n    const stopMove = () => {\n      isHover.value = true;\n\n      if (singleWaitTimeout.value) {\n        clearTimeout(singleWaitTimeout.value);\n      }\n\n      cancle();\n    };\n\n    const hoverStop = computed(() => props.hover && props.modelValue && isScroll.value);\n    const throttleFunc = throttle(30, e => {\n      cancle();\n      const singleHeight = !!realSingleStopHeight.value ? realSingleStopHeight.value : 15;\n\n      if (e.deltaY < 0) {\n        animation(\"down\", singleHeight, true);\n      }\n\n      if (e.deltaY > 0) {\n        animation(\"up\", singleHeight, true);\n      }\n    });\n\n    const onWheel = e => {\n      throttleFunc(e);\n    };\n\n    const reset = () => {\n      cancle();\n      isHover.value = false;\n      initMove();\n    };\n\n    const Reset = () => {\n      reset();\n    };\n\n    useExpose({\n      Reset\n    });\n    watch(() => props.list, () => {\n      if (props.isWatch) {\n        nextTick(() => {\n          reset();\n        });\n      }\n    }, {\n      deep: true\n    });\n    watch(() => props.modelValue, newValue => {\n      if (newValue) {\n        startMove();\n      } else {\n        stopMove();\n      }\n    });\n    watch(() => props.count, newValue => {\n      if (newValue !== 0) {\n        startMove();\n      }\n    });\n    onBeforeMount(() => {\n      cancle();\n      clearTimeout(singleWaitTimeout.value);\n    });\n    onMounted(() => {\n      if (isScroll.value) {\n        initMove();\n      }\n    });\n    const {\n      default: $default,\n      html\n    } = slots;\n    const copyNum = new Array(props.copyNum).fill(null);\n\n    const getHtml = () => {\n      return createVNode(Fragment, null, [createVNode(\"div\", {\n        \"ref\": slotListRef,\n        \"style\": floatStyle.value\n      }, [$default && $default()]), isScroll.value ? copyNum.map(() => {\n        if (html && typeof html === \"function\") {\n          return createVNode(\"div\", {\n            \"style\": floatStyle.value\n          }, [html()]);\n        } else {\n          return createVNode(\"div\", {\n            \"style\": floatStyle.value\n          }, [$default && $default()]);\n        }\n      }) : null]);\n    };\n\n    return () => createVNode(\"div\", {\n      \"ref\": scrollRef,\n      \"class\": attrs.class\n    }, [props.wheel && props.hover ? createVNode(\"div\", {\n      \"ref\": realBoxRef,\n      \"style\": realBoxStyle.value,\n      \"onMouseenter\": () => {\n        if (hoverStop.value) {\n          stopMove();\n        }\n      },\n      \"onMouseleave\": () => {\n        if (hoverStop.value) {\n          startMove();\n        }\n      },\n      \"onWheel\": e => {\n        if (hoverStop.value) {\n          onWheel(e);\n        }\n      }\n    }, [getHtml()]) : createVNode(\"div\", {\n      \"ref\": realBoxRef,\n      \"style\": realBoxStyle.value,\n      \"onMouseenter\": () => {\n        if (hoverStop.value) {\n          stopMove();\n        }\n      },\n      \"onMouseleave\": () => {\n        if (hoverStop.value) {\n          startMove();\n        }\n      }\n    }, [getHtml()])]);\n  }\n\n});\n\nconst install = function (app, options = {}) {\n  app.component(options.name || Vue3SeamlessScroll.name, Vue3SeamlessScroll);\n};\n\nfunction index (app) {\n  app.use(install);\n}\n\nexport { Vue3SeamlessScroll, index as default };\n"], "mappings": ";;;;;;;;;;;;;;;AAoBA,SAAS,SAAU,OAAO,YAAY,UAAU,cAAc;AAM5D,MAAI;AACJ,MAAI,YAAY;AAEhB,MAAI,WAAW;AAEf,WAAS,uBAAuB;AAC9B,QAAI,WAAW;AACb,mBAAa,SAAS;AAAA,IACxB;AAAA,EACF;AAGA,WAAS,SAAS;AAChB,yBAAqB;AACrB,gBAAY;AAAA,EACd;AAGA,MAAI,OAAO,eAAe,WAAW;AACnC,mBAAe;AACf,eAAW;AACX,iBAAa;AAAA,EACf;AAQA,WAAS,UAAU;AACjB,aAAS,OAAO,UAAU,QAAQ,aAAa,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AAC7F,iBAAW,IAAI,IAAI,UAAU,IAAI;AAAA,IACnC;AAEA,QAAI,OAAO;AACX,QAAI,UAAU,KAAK,IAAI,IAAI;AAE3B,QAAI,WAAW;AACb;AAAA,IACF;AAGA,aAAS,OAAO;AACd,iBAAW,KAAK,IAAI;AACpB,eAAS,MAAM,MAAM,UAAU;AAAA,IACjC;AAOA,aAAS,QAAQ;AACf,kBAAY;AAAA,IACd;AAEA,QAAI,gBAAgB,CAAC,WAAW;AAK9B,WAAK;AAAA,IACP;AAEA,yBAAqB;AAErB,QAAI,iBAAiB,UAAa,UAAU,OAAO;AAKjD,WAAK;AAAA,IACP,WAAW,eAAe,MAAM;AAY9B,kBAAY,WAAW,eAAe,QAAQ,MAAM,iBAAiB,SAAY,QAAQ,UAAU,KAAK;AAAA,IAC1G;AAAA,EACF;AAEA,UAAQ,SAAS;AAEjB,SAAO;AACT;AAEA,SAAS,UAAU,MAAM;AACvB,QAAM,WAAW,mBAAmB;AAEpC,MAAI,UAAU;AACZ,WAAO,OAAO,SAAS,OAAO,IAAI;AAAA,EACpC;AACF;AAEA,IAAM,QAAQ;AAAA;AAAA,EAEZ,YAAY;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA,EAEA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,UAAU;AAAA,IACV,SAAS,CAAC;AAAA,EACZ;AAAA;AAAA,EAEA,MAAM;AAAA,IACJ,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA,EAEA,gBAAgB;AAAA,IACd,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA,EAEA,OAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA,EAEA,WAAW;AAAA,IACT,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA,EAEA,cAAc;AAAA,IACZ,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA,EAEA,aAAa;AAAA,IACX,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA,EAEA,gBAAgB;AAAA,IACd,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA,EAEA,WAAW;AAAA,IACT,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA,EAEA,SAAS;AAAA,IACP,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA,EAEA,OAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA,EAEA,MAAM;AAAA,IACJ,MAAM,CAAC,QAAQ,MAAM;AAAA,IACrB,SAAS;AAAA,EACX;AAAA;AAAA,EAEA,OAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA,EAEA,SAAS;AAAA,IACP,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA,EAEA,OAAO;AAAA,IACL,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AAAA;AAAA,EAEA,YAAY;AAAA,IACV,MAAM;AAAA,IACN,SAAS;AAAA,EACX;AACF;AAEA,WAAW,OAAO,uBAAuB,WAAY;AACnD,SAAO,WAAW,OAAO;AAAA,EACzB,WAAW,OAAO;AAAA,EAClB,WAAW,OAAO;AAAA,EAClB,WAAW,OAAO;AAAA,EAClB,WAAW,OAAO,0BAA0B,SAAU,IAAI;AACxD,WAAO,WAAW,OAAO,aAAa,EAAE;AAAA,EAC1C;AACF,EAAE;AAEF,WAAW,OAAO,wBAAwB,WAAY;AACpD,SAAO,WAAW,OAAO;AAAA,EACzB,WAAW,OAAO;AAAA,EAClB,WAAW,OAAO;AAAA,EAClB,WAAW,OAAO;AAAA,EAClB,WAAW,OAAO,2BAA2B,SAAU,UAAU;AAC/D,WAAO,WAAW,OAAO,WAAW,UAAU,MAAO,EAAE;AAAA,EACzD;AACF,EAAE;AAEF,SAAS,SAAS,MAAM;AACtB,MAAI,QAAQ,OAAO,SAAS,aAAa,KAAK,SAAS,KAAK;AAC1D,YAAQ,KAAK,QAAQ,KAAK,MAAM,wBAAwB;AAAA,EAC1D;AACF;AAEA,IAAM,qBAAqB,gBAAgB;AAAA,EACzC,MAAM;AAAA,EACN,cAAc;AAAA,EACd,OAAO;AAAA,EACP,OAAO,CAAC,QAAQ,SAAS,MAAM;AAAA,EAE/B,MAAM,QAAQ;AAAA,IACZ;AAAA,IACA;AAAA,IACA;AAAA,EACF,GAAG;AACD,UAAM,QAAQ;AACd,UAAM,YAAY,IAAI,IAAI;AAC1B,UAAM,cAAc,IAAI,IAAI;AAC5B,UAAM,aAAa,IAAI,IAAI;AAC3B,UAAM,WAAW,IAAI,IAAI;AACzB,UAAM,oBAAoB,IAAI,IAAI;AAClC,UAAM,eAAe,IAAI,CAAC;AAC1B,UAAM,gBAAgB,IAAI,CAAC;AAC3B,UAAM,OAAO,IAAI,CAAC;AAClB,UAAM,OAAO,IAAI,CAAC;AAClB,UAAM,UAAU,IAAI,KAAK;AAEzB,UAAM,SAAS,IAAI,CAAC;AAEpB,UAAM,WAAW,SAAS,MAAM,MAAM,OAAO,MAAM,KAAK,UAAU,MAAM,iBAAiB,KAAK;AAC9F,UAAM,eAAe,SAAS,MAAM;AAClC,aAAO;AAAA,QACL,OAAO,aAAa,QAAQ,GAAG,aAAa,KAAK,OAAO;AAAA,QACxD,WAAW,aAAa,KAAK,KAAK,MAAM,KAAK,KAAK;AAAA;AAAA,QAElD,YAAY,OAAO,OAAO,MAAM,SAAS,WAAW,MAAM,OAAO,kBAAkB,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,MAAM,MAAM,KAAK,KAAK,GAAG,IAAI,MAAM,KAAK;AAAA,QACvL,UAAU;AAAA,QACV,SAAS,MAAM,aAAa,SAAS;AAAA,MACvC;AAAA,IACF,CAAC;AACD,UAAM,eAAe,SAAS,MAAM,MAAM,aAAa,UAAU,MAAM,aAAa,OAAO;AAC3F,UAAM,aAAa,SAAS,MAAM;AAChC,aAAO,aAAa,QAAQ;AAAA,QAC1B,OAAO;AAAA,QACP,UAAU;AAAA,QACV,SAAS,MAAM,aAAa,SAAS;AAAA,QACrC,YAAY,MAAM,aAAa,IAAI;AAAA,MACrC,IAAI;AAAA,QACF,UAAU;AAAA,MACZ;AAAA,IACF,CAAC;AACD,UAAM,eAAe,SAAS,MAAM;AAClC,aAAO,MAAM,YAAY,SAAS,WAAW,OAAO,iBAAiB,WAAW,SAAS,iBAAiB,IAAI,EAAE,QAAQ,IAAI;AAAA,IAC9H,CAAC;AACD,UAAM,sBAAsB,SAAS,MAAM,MAAM,cAAc,aAAa,KAAK;AACjF,UAAM,uBAAuB,SAAS,MAAM,MAAM,eAAe,aAAa,KAAK;AACnF,UAAM,OAAO,SAAS,MAAM;AAC1B,UAAI;AACJ,UAAI,QAAQ,MAAM;AAElB,UAAI,aAAa,OAAO;AACtB,qBAAa,oBAAoB;AAAA,MACnC,OAAO;AACL,qBAAa,qBAAqB;AAAA,MACpC;AAEA,UAAI,aAAa,KAAK,aAAa,QAAQ,GAAG;AAC5C,gBAAQ,MAAM,oDAAoD;AAAA,MACpE;AAEA,aAAO;AAAA,IACT,CAAC;AAED,UAAM,SAAS,MAAM;AACnB,2BAAqB,SAAS,KAAK;AACnC,eAAS,QAAQ;AAAA,IACnB;AAEA,UAAM,YAAY,CAAC,YAAY,OAAO,YAAY;AAChD,eAAS,QAAQ,sBAAsB,WAAY;AACjD,cAAM,IAAI,cAAc,QAAQ;AAChC,cAAM,IAAI,aAAa,QAAQ;AAE/B,YAAI,eAAe,MAAM;AACvB,cAAI,KAAK,IAAI,KAAK,KAAK,KAAK,GAAG;AAC7B,iBAAK,QAAQ;AACb,mBAAO,SAAS;AAChB,iBAAK,SAAS,OAAO,KAAK;AAAA,UAC5B;AAEA,eAAK,SAAS;AAAA,QAChB,WAAW,eAAe,QAAQ;AAChC,cAAI,KAAK,SAAS,GAAG;AACnB,iBAAK,QAAQ,IAAI;AACjB,mBAAO,SAAS;AAChB,iBAAK,SAAS,OAAO,KAAK;AAAA,UAC5B;AAEA,eAAK,SAAS;AAAA,QAChB,WAAW,eAAe,QAAQ;AAChC,cAAI,KAAK,IAAI,KAAK,KAAK,KAAK,GAAG;AAC7B,iBAAK,QAAQ;AACb,mBAAO,SAAS;AAChB,iBAAK,SAAS,OAAO,KAAK;AAAA,UAC5B;AAEA,eAAK,SAAS;AAAA,QAChB,WAAW,eAAe,SAAS;AACjC,cAAI,KAAK,SAAS,GAAG;AACnB,iBAAK,QAAQ,IAAI;AACjB,mBAAO,SAAS;AAChB,iBAAK,SAAS,OAAO,KAAK;AAAA,UAC5B;AAEA,eAAK,SAAS;AAAA,QAChB;AAEA,YAAI,SAAS;AACX;AAAA,QACF;AAEA,YAAI;AAAA,UACF;AAAA,QACF,IAAI;AAEJ,YAAI,kBAAkB,OAAO;AAC3B,uBAAa,kBAAkB,KAAK;AAAA,QACtC;AAEA,YAAI,CAAC,CAAC,qBAAqB,OAAO;AAChC,cAAI,KAAK,IAAI,KAAK,KAAK,IAAI,qBAAqB,QAAQ,OAAO;AAC7D,8BAAkB,QAAQ,WAAW,MAAM;AACzC,mBAAK;AAAA,YACP,GAAG,cAAc;AAAA,UACnB,OAAO;AACL,iBAAK;AAAA,UACP;AAAA,QACF,WAAW,CAAC,CAAC,oBAAoB,OAAO;AACtC,cAAI,KAAK,IAAI,KAAK,KAAK,IAAI,oBAAoB,QAAQ,OAAO;AAC5D,8BAAkB,QAAQ,WAAW,MAAM;AACzC,mBAAK;AAAA,YACP,GAAG,cAAc;AAAA,UACnB,OAAO;AACL,iBAAK;AAAA,UACP;AAAA,QACF,OAAO;AACL,eAAK;AAAA,QACP;AAAA,MACF,CAAC;AAAA,IACH;AAEA,UAAM,OAAO,MAAM;AACjB,aAAO;AAEP,UAAI,QAAQ,SAAS,CAAC,SAAS,SAAS,OAAO,UAAU,MAAM,OAAO;AACpE,aAAK,QAAQ,OAAO,KAAK;AACzB,eAAO,QAAQ;AACf;AAAA,MACF;AAEA,gBAAU,MAAM,WAAW,KAAK,OAAO,KAAK;AAAA,IAC9C;AAEA,UAAM,WAAW,MAAM;AACrB,eAAS,MAAM,IAAI;AAEnB,UAAI,aAAa,OAAO;AACtB,YAAI,gBAAgB,YAAY,MAAM;AACtC,wBAAgB,gBAAgB,IAAI;AACpC,qBAAa,QAAQ;AAAA,MACvB;AAEA,UAAI,SAAS,OAAO;AAClB,sBAAc,QAAQ,WAAW,MAAM;AAEvC,YAAI,MAAM,YAAY;AACpB,eAAK;AAAA,QACP;AAAA,MACF,OAAO;AACL,eAAO;AACP,aAAK,QAAQ,KAAK,QAAQ;AAAA,MAC5B;AAAA,IACF;AAEA,UAAM,YAAY,MAAM;AACtB,cAAQ,QAAQ;AAChB,WAAK;AAAA,IACP;AAEA,UAAM,WAAW,MAAM;AACrB,cAAQ,QAAQ;AAEhB,UAAI,kBAAkB,OAAO;AAC3B,qBAAa,kBAAkB,KAAK;AAAA,MACtC;AAEA,aAAO;AAAA,IACT;AAEA,UAAM,YAAY,SAAS,MAAM,MAAM,SAAS,MAAM,cAAc,SAAS,KAAK;AAClF,UAAM,eAAe,SAAS,IAAI,OAAK;AACrC,aAAO;AACP,YAAM,eAAe,CAAC,CAAC,qBAAqB,QAAQ,qBAAqB,QAAQ;AAEjF,UAAI,EAAE,SAAS,GAAG;AAChB,kBAAU,QAAQ,cAAc,IAAI;AAAA,MACtC;AAEA,UAAI,EAAE,SAAS,GAAG;AAChB,kBAAU,MAAM,cAAc,IAAI;AAAA,MACpC;AAAA,IACF,CAAC;AAED,UAAM,UAAU,OAAK;AACnB,mBAAa,CAAC;AAAA,IAChB;AAEA,UAAM,QAAQ,MAAM;AAClB,aAAO;AACP,cAAQ,QAAQ;AAChB,eAAS;AAAA,IACX;AAEA,UAAM,QAAQ,MAAM;AAClB,YAAM;AAAA,IACR;AAEA,cAAU;AAAA,MACR;AAAA,IACF,CAAC;AACD,UAAM,MAAM,MAAM,MAAM,MAAM;AAC5B,UAAI,MAAM,SAAS;AACjB,iBAAS,MAAM;AACb,gBAAM;AAAA,QACR,CAAC;AAAA,MACH;AAAA,IACF,GAAG;AAAA,MACD,MAAM;AAAA,IACR,CAAC;AACD,UAAM,MAAM,MAAM,YAAY,cAAY;AACxC,UAAI,UAAU;AACZ,kBAAU;AAAA,MACZ,OAAO;AACL,iBAAS;AAAA,MACX;AAAA,IACF,CAAC;AACD,UAAM,MAAM,MAAM,OAAO,cAAY;AACnC,UAAI,aAAa,GAAG;AAClB,kBAAU;AAAA,MACZ;AAAA,IACF,CAAC;AACD,kBAAc,MAAM;AAClB,aAAO;AACP,mBAAa,kBAAkB,KAAK;AAAA,IACtC,CAAC;AACD,cAAU,MAAM;AACd,UAAI,SAAS,OAAO;AAClB,iBAAS;AAAA,MACX;AAAA,IACF,CAAC;AACD,UAAM;AAAA,MACJ,SAAS;AAAA,MACT;AAAA,IACF,IAAI;AACJ,UAAM,UAAU,IAAI,MAAM,MAAM,OAAO,EAAE,KAAK,IAAI;AAElD,UAAM,UAAU,MAAM;AACpB,aAAO,YAAY,UAAU,MAAM,CAAC,YAAY,OAAO;AAAA,QACrD,OAAO;AAAA,QACP,SAAS,WAAW;AAAA,MACtB,GAAG,CAAC,YAAY,SAAS,CAAC,CAAC,GAAG,SAAS,QAAQ,QAAQ,IAAI,MAAM;AAC/D,YAAI,QAAQ,OAAO,SAAS,YAAY;AACtC,iBAAO,YAAY,OAAO;AAAA,YACxB,SAAS,WAAW;AAAA,UACtB,GAAG,CAAC,KAAK,CAAC,CAAC;AAAA,QACb,OAAO;AACL,iBAAO,YAAY,OAAO;AAAA,YACxB,SAAS,WAAW;AAAA,UACtB,GAAG,CAAC,YAAY,SAAS,CAAC,CAAC;AAAA,QAC7B;AAAA,MACF,CAAC,IAAI,IAAI,CAAC;AAAA,IACZ;AAEA,WAAO,MAAM,YAAY,OAAO;AAAA,MAC9B,OAAO;AAAA,MACP,SAAS,MAAM;AAAA,IACjB,GAAG,CAAC,MAAM,SAAS,MAAM,QAAQ,YAAY,OAAO;AAAA,MAClD,OAAO;AAAA,MACP,SAAS,aAAa;AAAA,MACtB,gBAAgB,MAAM;AACpB,YAAI,UAAU,OAAO;AACnB,mBAAS;AAAA,QACX;AAAA,MACF;AAAA,MACA,gBAAgB,MAAM;AACpB,YAAI,UAAU,OAAO;AACnB,oBAAU;AAAA,QACZ;AAAA,MACF;AAAA,MACA,WAAW,OAAK;AACd,YAAI,UAAU,OAAO;AACnB,kBAAQ,CAAC;AAAA,QACX;AAAA,MACF;AAAA,IACF,GAAG,CAAC,QAAQ,CAAC,CAAC,IAAI,YAAY,OAAO;AAAA,MACnC,OAAO;AAAA,MACP,SAAS,aAAa;AAAA,MACtB,gBAAgB,MAAM;AACpB,YAAI,UAAU,OAAO;AACnB,mBAAS;AAAA,QACX;AAAA,MACF;AAAA,MACA,gBAAgB,MAAM;AACpB,YAAI,UAAU,OAAO;AACnB,oBAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAAA,EAClB;AAEF,CAAC;AAED,IAAM,UAAU,SAAU,KAAK,UAAU,CAAC,GAAG;AAC3C,MAAI,UAAU,QAAQ,QAAQ,mBAAmB,MAAM,kBAAkB;AAC3E;AAEA,SAAS,MAAO,KAAK;AACnB,MAAI,IAAI,OAAO;AACjB;", "names": []}